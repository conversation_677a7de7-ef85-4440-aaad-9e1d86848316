import streamDeck, { LogLevel } from "@elgato/streamdeck";

import { MSpaControl } from "./actions/mspa-control";

// Set production log level to INFO for user support and debugging
streamDeck.logger.setLevel(LogLevel.INFO);

// Log plugin startup
streamDeck.logger.info('MSpa Plugin starting up...');

// Register the MSPA control action.
streamDeck.actions.registerAction(new MSpaControl());

// Finally, connect to the Stream Deck.
streamDeck.connect();

streamDeck.logger.info('MSpa Plugin initialized successfully');
