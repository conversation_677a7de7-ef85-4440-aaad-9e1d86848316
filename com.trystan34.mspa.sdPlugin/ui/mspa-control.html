<!doctype html>
<html>
<head lang="en">
    <meta charset="utf-8" />
    <title>MSpa Control</title>
    <script src="../sdpi-components.js"></script>
</head>
<body>
    <sdpi-item label="Account Email">
        <sdpi-textfield 
            setting="email" 
            type="email" 
            placeholder="Enter your MSPA account email"
            required>
        </sdpi-textfield>
    </sdpi-item>

    <sdpi-item label="Password">
        <sdpi-password 
            setting="password"
            placeholder="Enter your password"
            required>
        </sdpi-password>
    </sdpi-item>

    <sdpi-item label="Device ID">
        <sdpi-textfield 
            setting="deviceId" 
            placeholder="Enter your device ID"
            required>
        </sdpi-textfield>
        <sdpi-info>
            This can be found in the MSpa mobile app.
        </sdpi-info>
    </sdpi-item>

    <sdpi-item label="Product ID">
        <sdpi-textfield 
            setting="productId" 
            placeholder="Enter your product ID"
            required>
        </sdpi-textfield>
        <sdpi-info>
            <a style="color: red;" href="https://github.com/elgatosf/streamdeck" target="_blank">See GitHub README for instructions.</a>
        </sdpi-info>
    </sdpi-item>

    <sdpi-item label="Temperature Unit">
        <sdpi-select setting="temperatureUnit" default="fahrenheit">
            <option value="fahrenheit" selected>Fahrenheit (°F)</option>
            <option value="celsius">Celsius (°C)</option>
        </sdpi-select>
    </sdpi-item>

    <sdpi-item label="Refresh Interval">
        <sdpi-select setting="refreshInterval" default="30">
            <option value="10">10 seconds</option>
            <option value="15">15 seconds</option>
            <option value="30" selected>30 seconds</option>
            <option value="60">1 minute</option>
            <option value="120">2 minutes</option>
            <option value="300">5 minutes</option>
        </sdpi-select>
        <sdpi-info>
            How often to check for status updates for your MSpa hot tub.
        </sdpi-info>
    </sdpi-item>

    <sdpi-item label="Status">
        <div style="margin-top:0.5em" id="connectionStatus">Not connected</div>
    </sdpi-item>

    <sdpi-button id="connectButton" disabled>
    <div id="connectButtonText">Connect</div>
    </sdpi-button>

    <script>
        const { streamDeckClient } = SDPIComponents;
        let settings = {};

        // Handle password hashing when settings change
        streamDeckClient.didReceiveSettings.subscribe((event) => {
            settings = event.payload.settings || {};
            console.log('Settings received:', settings);
            updateConnectionStatus();
        });

        // Handle when individual settings change
        streamDeckClient.sendToPropertyInspector.subscribe((event) => {
            console.log('Message from plugin:', event);
        });

        // Custom event handling for password field
        document.addEventListener('change', (event) => {
            if (event.target.getAttribute('setting') === 'password' && event.target.value && event.target.value.trim() !== '') {
                // Hash password before saving
                const hashedPassword = md5(event.target.value);
                console.log('Password hashed');
                
                // Get current settings and add hashed password
                streamDeckClient.getSettings().then(currentSettings => {
                    const newSettings = {
                        ...currentSettings,
                        passwordHash: hashedPassword
                    };
                    
                    // Remove plain password from settings
                    delete newSettings.password;
                    
                    settings = newSettings;
                    streamDeckClient.setSettings(newSettings);
                    
                    // Clear the password field
                    event.target.value = '';
                    updateConnectionStatus();
                });
            } else {
                updateConnectionStatus();
            }
        });

        function updateConnectionStatus() {
            const statusDiv = document.getElementById('connectionStatus');
            const connectButton = document.getElementById('connectButton');
            const hasCredentials = settings.email && settings.passwordHash && settings.deviceId && settings.productId;
            
            if (hasCredentials) {
                statusDiv.textContent = 'Credentials configured';
                statusDiv.style.color = '#00ff00';
                connectButton.disabled = false;
            } else {
                statusDiv.textContent = 'Missing credentials';
                statusDiv.style.color = '#ff4444';
                connectButton.disabled = true;
            }
        }

        // Handle connect button click
        async function testConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            const connectButton = document.getElementById('connectButton');
            const connectButtonText = document.getElementById('connectButtonText');
            
            // Disable button and show connecting status
            connectButton.disabled = true;
            connectButtonText.textContent = 'Connecting...';
            statusDiv.textContent = 'Testing connection...';
            statusDiv.style.color = '#ffaa00';
            
            try {
                // Test authentication with MSPA API
                const authResult = await testMSPAAuthentication(settings);
                
                if (authResult.success) {
                    statusDiv.textContent = 'Connected successfully!';
                    statusDiv.style.color = '#00ff00';
                    connectButtonText.textContent = 'Connected ✓';
                    
                    // Reset button after 3 seconds
                    setTimeout(() => {
                        connectButtonText.textContent = 'Connect';
                        connectButton.disabled = false;
                    }, 3000);
                } else {
                    statusDiv.textContent = `Connection failed: ${JSON.stringify(authResult)}`;
                    statusDiv.style.color = '#ff4444';
                    connectButtonText.textContent = 'Connect';
                    connectButton.disabled = false;
                }
            } catch (error) {
                statusDiv.textContent = `Connection error: ${error.message}`;
                statusDiv.style.color = '#ff4444';
                connectButtonText.textContent = 'Connect';
                connectButton.disabled = false;
            }
        }

        // Test MSPA API authentication
        async function testMSPAAuthentication(settings) {
            if (!settings.email || !settings.passwordHash || !settings.deviceId || !settings.productId) {
                return { success: false, error: 'Missing credentials' };
            }
            
            try {
                // Use the same API approach as the plugin
                const nonce = generateNonce();
                const ts = currentTimestamp();
                const sign = buildSignature('e1c8e068f9ca11eba4dc0242ac120002', nonce, ts, '87025c9ecd18906d27225fe79cb68349');
                
                const headers = {
                    'push_type': 'Android',
                    'authorization': 'token',
                    'appid': 'e1c8e068f9ca11eba4dc0242ac120002',
                    'nonce': nonce,
                    'ts': ts,
                    'lan_code': 'EN',
                    'sign': sign,
                    'content-type': 'application/json; charset=UTF-8',
                    'accept-encoding': 'gzip',
                    'user-agent': 'okhttp/4.9.0',
                };

                const payload = {
                    account: settings.email,
                    app_id: 'e1c8e068f9ca11eba4dc0242ac120002',
                    password: settings.passwordHash,
                    brand: '',
                    registration_id: '',
                    push_type: 'android',
                    lan_code: 'EN',
                    country: '',
                };

                const response = await fetch('https://api.iot.the-mspa.com/api/enduser/get_token/', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload),
                });

                const data = await response.json();
                
                if (data?.data?.token) {
                    // Test device status with the token
                    return await testDeviceStatus(data.data.token, settings);
                } else {
                    return { success: false, reponse: data };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Test device status call
        async function testDeviceStatus(token, settings) {
            try {
                const nonce = generateNonce();
                const ts = currentTimestamp();
                const sign = buildSignature('e1c8e068f9ca11eba4dc0242ac120002', nonce, ts, '87025c9ecd18906d27225fe79cb68349');

                const headers = {
                    'push_type': 'Android',
                    'authorization': `token ${token}`,
                    'appid': 'e1c8e068f9ca11eba4dc0242ac120002',
                    'nonce': nonce,
                    'ts': ts,
                    'lan_code': 'EN',
                    'sign': sign,
                    'content-type': 'application/json; charset=UTF-8',
                    'accept-encoding': 'gzip',
                    'user-agent': 'okhttp/4.9.0',
                };

                const payload = {
                    device_id: settings.deviceId,
                    product_id: settings.productId,
                };

                const response = await fetch('https://api.iot.the-mspa.com/api/device/thing_shadow/', {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(payload),
                });

                const data = await response.json();
                
                if (data.data.ConnectType) {
                    return { success: true, data: data.data };
                } else {
                    return { success: false, error: data?.msg || 'Device or Product not found' };
                }
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Utility functions for API calls
        function generateNonce(length = 32) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        function currentTimestamp() {
            return Math.floor(Date.now() / 1000).toString();
        }

        function buildSignature(appId, nonce, ts, appSecret) {
            const raw = `${appId},${appSecret},${nonce},${ts}`;
            return md5(raw).toUpperCase();
        }

        // Convert boolean values to numbers (1/0) for MSPA API compatibility
        function convertBooleansToNumbers(obj) {
            if (obj === null || obj === undefined) {
                return obj;
            }
            
            if (typeof obj === 'boolean') {
                return obj ? 1 : 0;
            }
            
            if (Array.isArray(obj)) {
                return obj.map(item => convertBooleansToNumbers(item));
            }
            
            if (typeof obj === 'object') {
                const converted = {};
                for (const [key, value] of Object.entries(obj)) {
                    converted[key] = convertBooleansToNumbers(value);
                }
                return converted;
            }
            
            return obj;
        }

        // Add event listener for connect button
        document.addEventListener('DOMContentLoaded', () => {
            const connectButton = document.getElementById('connectButton');
            connectButton.addEventListener('click', testConnection);
        });

        // Simple MD5 implementation
        function md5(string) {
            function md5cycle(x, k) {
                var a = x[0], b = x[1], c = x[2], d = x[3];
                a = ff(a, b, c, d, k[0], 7, -680876936);
                d = ff(d, a, b, c, k[1], 12, -389564586);
                c = ff(c, d, a, b, k[2], 17, 606105819);
                b = ff(b, c, d, a, k[3], 22, -1044525330);
                a = ff(a, b, c, d, k[4], 7, -176418897);
                d = ff(d, a, b, c, k[5], 12, 1200080426);
                c = ff(c, d, a, b, k[6], 17, -1473231341);
                b = ff(b, c, d, a, k[7], 22, -45705983);
                a = ff(a, b, c, d, k[8], 7, 1770035416);
                d = ff(d, a, b, c, k[9], 12, -1958414417);
                c = ff(c, d, a, b, k[10], 17, -42063);
                b = ff(b, c, d, a, k[11], 22, -1990404162);
                a = ff(a, b, c, d, k[12], 7, 1804603682);
                d = ff(d, a, b, c, k[13], 12, -40341101);
                c = ff(c, d, a, b, k[14], 17, -1502002290);
                b = ff(b, c, d, a, k[15], 22, 1236535329);
                a = gg(a, b, c, d, k[1], 5, -165796510);
                d = gg(d, a, b, c, k[6], 9, -1069501632);
                c = gg(c, d, a, b, k[11], 14, 643717713);
                b = gg(b, c, d, a, k[0], 20, -373897302);
                a = gg(a, b, c, d, k[5], 5, -701558691);
                d = gg(d, a, b, c, k[10], 9, 38016083);
                c = gg(c, d, a, b, k[15], 14, -660478335);
                b = gg(b, c, d, a, k[4], 20, -405537848);
                a = gg(a, b, c, d, k[9], 5, 568446438);
                d = gg(d, a, b, c, k[14], 9, -1019803690);
                c = gg(c, d, a, b, k[3], 14, -187363961);
                b = gg(b, c, d, a, k[8], 20, 1163531501);
                a = gg(a, b, c, d, k[13], 5, -1444681467);
                d = gg(d, a, b, c, k[2], 9, -51403784);
                c = gg(c, d, a, b, k[7], 14, 1735328473);
                b = gg(b, c, d, a, k[12], 20, -1926607734);
                a = hh(a, b, c, d, k[5], 4, -378558);
                d = hh(d, a, b, c, k[8], 11, -2022574463);
                c = hh(c, d, a, b, k[11], 16, 1839030562);
                b = hh(b, c, d, a, k[14], 23, -35309556);
                a = hh(a, b, c, d, k[1], 4, -1530992060);
                d = hh(d, a, b, c, k[4], 11, 1272893353);
                c = hh(c, d, a, b, k[7], 16, -155497632);
                b = hh(b, c, d, a, k[10], 23, -1094730640);
                a = hh(a, b, c, d, k[13], 4, 681279174);
                d = hh(d, a, b, c, k[0], 11, -358537222);
                c = hh(c, d, a, b, k[3], 16, -722521979);
                b = hh(b, c, d, a, k[6], 23, 76029189);
                a = hh(a, b, c, d, k[9], 4, -640364487);
                d = hh(d, a, b, c, k[12], 11, -421815835);
                c = hh(c, d, a, b, k[15], 16, 530742520);
                b = hh(b, c, d, a, k[2], 23, -995338651);
                a = ii(a, b, c, d, k[0], 6, -198630844);
                d = ii(d, a, b, c, k[7], 10, 1126891415);
                c = ii(c, d, a, b, k[14], 15, -1416354905);
                b = ii(b, c, d, a, k[5], 21, -57434055);
                a = ii(a, b, c, d, k[12], 6, 1700485571);
                d = ii(d, a, b, c, k[3], 10, -1894986606);
                c = ii(c, d, a, b, k[10], 15, -1051523);
                b = ii(b, c, d, a, k[1], 21, -2054922799);
                a = ii(a, b, c, d, k[8], 6, 1873313359);
                d = ii(d, a, b, c, k[15], 10, -30611744);
                c = ii(c, d, a, b, k[6], 15, -1560198380);
                b = ii(b, c, d, a, k[13], 21, 1309151649);
                a = ii(a, b, c, d, k[4], 6, -145523070);
                d = ii(d, a, b, c, k[11], 10, -1120210379);
                c = ii(c, d, a, b, k[2], 15, 718787259);
                b = ii(b, c, d, a, k[9], 21, -343485551);
                x[0] = add32(a, x[0]);
                x[1] = add32(b, x[1]);
                x[2] = add32(c, x[2]);
                x[3] = add32(d, x[3]);
            }
            
            function cmn(q, a, b, x, s, t) {
                a = add32(add32(a, q), add32(x, t));
                return add32((a << s) | (a >>> (32 - s)), b);
            }
            
            function ff(a, b, c, d, x, s, t) {
                return cmn((b & c) | ((~b) & d), a, b, x, s, t);
            }
            
            function gg(a, b, c, d, x, s, t) {
                return cmn((b & d) | (c & (~d)), a, b, x, s, t);
            }
            
            function hh(a, b, c, d, x, s, t) {
                return cmn(b ^ c ^ d, a, b, x, s, t);
            }
            
            function ii(a, b, c, d, x, s, t) {
                return cmn(c ^ (b | (~d)), a, b, x, s, t);
            }
            
            function md51(s) {
                var n = s.length, state = [1732584193, -271733879, -1732584194, 271733878], i;
                for (i = 64; i <= s.length; i += 64) {
                    md5cycle(state, md5blk(s.substring(i - 64, i)));
                }
                s = s.substring(i - 64);
                var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
                for (i = 0; i < s.length; i++)
                    tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
                tail[i >> 2] |= 0x80 << ((i % 4) << 3);
                if (i > 55) {
                    md5cycle(state, tail);
                    for (i = 0; i < 16; i++) tail[i] = 0;
                }
                tail[14] = n * 8;
                md5cycle(state, tail);
                return state;
            }
            
            function md5blk(s) {
                var md5blks = [], i;
                for (i = 0; i < 64; i += 4) {
                    md5blks[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
                }
                return md5blks;
            }
            
            function rhex(n) {
                var hex_chr = '0123456789abcdef'.split('');
                var s = '', j = 0;
                for (; j < 4; j++)
                    s += hex_chr[(n >> (j * 8 + 4)) & 0x0F] + hex_chr[(n >> (j * 8)) & 0x0F];
                return s;
            }
            
            function hex(x) {
                for (var i = 0; i < x.length; i++)
                    x[i] = rhex(x[i]);
                return x.join('');
            }
            
            function add32(a, b) {
                return (a + b) & 0xFFFFFFFF;
            }
            
            return hex(md51(string));
        }
    </script>
</body>
</html>