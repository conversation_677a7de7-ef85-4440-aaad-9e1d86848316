{"packageManager": "pnpm@10.12.4", "scripts": {"build": "rollup -c", "watch": "rollup -c -w --watch.onEnd=\"streamdeck restart com.trystan34.mspa\""}, "type": "module", "devDependencies": {"@elgato/cli": "^1.4.0", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.2.2", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.0", "@tsconfig/node20": "^20.1.2", "@types/node": "~20.15.0", "rollup": "^4.0.2", "tslib": "^2.6.2", "typescript": "^5.2.2"}, "dependencies": {"@elgato/streamdeck": "^1.0.0"}}